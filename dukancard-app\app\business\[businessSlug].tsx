import React, { useState, useEffect, useCallback } from 'react';
import {
  View,
  StyleSheet,
  SafeAreaView,
  TouchableOpacity,
  StatusBar,
} from 'react-native';
import { useLocalSearchParams, useRouter } from 'expo-router';
import { ArrowLeft, Home, Search, Users, Store, User } from 'lucide-react-native';
import { useColorScheme } from '@/hooks/useColorScheme';
import { discoverBusinessForUser, BusinessDiscoveryData } from '../../lib/services/businessDiscovery';
import PublicCardView from '../../components/business/PublicCardView';
import { LoadingSpinner } from '../../components/shared/ui/LoadingSpinner';
import { EmptyState } from '../../components/shared/ui/EmptyState';
import { Toast } from '../../lib/utils/toast';
import { DukancardBottomTabs } from '../../components/shared/navigation/BottomNavigation';

export default function BusinessCardScreen() {
  const { businessSlug } = useLocalSearchParams<{ businessSlug: string }>();
  const router = useRouter();
  const colorScheme = useColorScheme();
  const isDark = colorScheme === 'dark';
  const [businessData, setBusinessData] = useState<BusinessDiscoveryData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Theme colors
  const backgroundColor = isDark ? '#000000' : '#FFFFFF';
  const textColor = isDark ? '#FFFFFF' : '#000000';
  const borderColor = isDark ? '#374151' : '#E5E7EB';
  const statusBarStyle = isDark ? 'light-content' : 'dark-content';

  const loadBusinessData = useCallback(async () => {
    if (!businessSlug) {
      setError('Invalid business URL');
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      setError(null);

      const result = await discoverBusinessForUser(businessSlug);

      if (result.success && result.data) {
        setBusinessData(result.data);
      } else {
        setError(result.error || 'Failed to load business information');
        Toast.show(result.error || 'Failed to load business information', 'error');
      }
    } catch (err) {
      console.error('Error loading business data:', err);
      setError('An unexpected error occurred');
      Toast.show('An unexpected error occurred', 'error');
    } finally {
      setLoading(false);
    }
  }, [businessSlug]);

  useEffect(() => {
    if (businessSlug) {
      loadBusinessData();
    }
  }, [businessSlug, loadBusinessData]);

  const handleBack = () => {
    // Navigate back to the appropriate dashboard
    if (router.canGoBack()) {
      router.back();
    } else {
      // Fallback to main app home instead of assuming user type
      router.replace('/');
    }
  };

  const handleRetry = () => {
    loadBusinessData();
  };

  // Bottom navigation tabs for public screens
  const tabs = [
    {
      key: 'home',
      icon: Home,
      label: 'Home',
      onPress: () => router.push('/'),
      isActive: false,
    },
    {
      key: 'discover',
      icon: Search,
      label: 'Discover',
      onPress: () => router.push('/discover'),
      isActive: false,
    },
    {
      key: 'feed',
      icon: Users,
      label: 'Feed',
      onPress: () => router.push('/feed'),
      isActive: false,
    },
    {
      key: 'account',
      icon: User,
      label: 'Account',
      onPress: () => router.push('/auth'),
      isActive: false,
    },
  ];

  if (loading) {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor }]}>
        <StatusBar barStyle={statusBarStyle} backgroundColor={backgroundColor} />
        <View style={[styles.header, { backgroundColor, borderBottomColor: borderColor }]}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={handleBack}
          >
            <ArrowLeft color={textColor} size={24} />
          </TouchableOpacity>
        </View>
        <View style={styles.centerContainer}>
          <LoadingSpinner size="large" />
        </View>
      </SafeAreaView>
    );
  }

  if (error || !businessData) {
    return (
      <SafeAreaView style={[styles.container, { backgroundColor }]}>
        <StatusBar barStyle={statusBarStyle} backgroundColor={backgroundColor} />
        <View style={[styles.header, { backgroundColor, borderBottomColor: borderColor }]}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={handleBack}
          >
            <ArrowLeft color={textColor} size={24} />
          </TouchableOpacity>
        </View>
        <View style={styles.centerContainer}>
          <EmptyState
            title="Business Not Found"
            description={error || 'This business could not be found or is currently unavailable.'}
            actionText="Try Again"
            onAction={handleRetry}
          />
        </View>
      </SafeAreaView>
    );
  }

  return (
    <View style={[styles.container, { backgroundColor }]}>
      <StatusBar barStyle="light-content" backgroundColor="transparent" translucent />
      <View style={{ flex: 1 }}>
        <PublicCardView businessData={businessData} onClose={handleBack} />
      </View>
      <DukancardBottomTabs
        tabs={tabs}
        colorScheme={colorScheme}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  centerContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 20,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
  },
  backButton: {
    padding: 8,
  },
});
