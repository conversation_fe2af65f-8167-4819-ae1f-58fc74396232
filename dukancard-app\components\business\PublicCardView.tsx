import React, { useState } from 'react';
import { View, ScrollView, Text } from 'react-native';
import { useColorScheme } from '@/hooks/useColorScheme';
import { BusinessDiscoveryData } from '../../lib/services/businessDiscovery';
import { useRouter } from 'expo-router';
import { LoadingSpinner } from '../shared/ui/LoadingSpinner';
import { createPublicCardViewStyles } from '../../styles/PublicCardViewStyles';
import ReviewModal from './ReviewModal';
import FullScreenImageViewer from './FullScreenImageViewer';

// Import new components
import PublicCardHeader from './PublicCardHeader';
import BusinessStats from './BusinessStats';
import FloatingInteractionButtons from './FloatingInteractionButtons';
import ContactActionButtons from './ContactActionButtons';
import SocialActionButtons from './SocialActionButtons';
import TabNavigation, { TabType } from './TabNavigation';
import AboutTab from './AboutTab';
import ProductsTab from './ProductsTab';
import GalleryTab from './GalleryTab';
import ReviewsTab from './ReviewsTab';

// Import custom hooks
import { useBusinessCardData, useProductsPagination, useReviewsPagination } from '../../hooks/useBusinessCardData';
import { useBusinessInteractions } from '../../hooks/useBusinessInteractions';

interface PublicCardViewProps {
  businessData: BusinessDiscoveryData;
  onClose?: () => void;
}

export default function PublicCardView({ businessData, onClose }: PublicCardViewProps) {
  const colorScheme = useColorScheme();
  const isDark = colorScheme === 'dark';
  const styles = createPublicCardViewStyles(isDark);
  const router = useRouter();

  const [selectedTab, setSelectedTab] = useState<TabType>('about');
  const [reviewModalVisible, setReviewModalVisible] = useState(false);
  const [fullScreenImageVisible, setFullScreenImageVisible] = useState(false);
  const [selectedImageIndex, setSelectedImageIndex] = useState(0);

  // Custom hooks
  const { cardData, loadingCardData } = useBusinessCardData(businessData.id, businessData.business_slug);
  const {
    interactionStatus,
    isOwner,
    likeLoading,
    subscribeLoading,
    handleLikePress,
    handleSubscribePress,
    handleReviewPress,
    handleReviewSubmitted,
  } = useBusinessInteractions(businessData.id);

  const {
    allProducts,
    loadingMoreProducts,
    hasMoreProducts,
    loadMoreProducts,
  } = useProductsPagination(businessData.id, cardData?.products || []);

  const {
    allReviews,
    loadingMoreReviews,
    hasMoreReviews,
    loadMoreReviews,
  } = useReviewsPagination(businessData.id, cardData?.reviews || []);

  // Handle gallery image press
  const handleGalleryImagePress = (index: number) => {
    setSelectedImageIndex(index);
    setFullScreenImageVisible(true);
  };

  // Handle product press
  const handleProductPress = (productId: string) => {
    router.push(`/product/${productId}`);
  };

  const renderTabContent = () => {
    if (loadingCardData) {
      return (
        <View style={styles.loadingContainer}>
          <LoadingSpinner size="small" />
          <Text style={styles.loadingText}>Loading...</Text>
        </View>
      );
    }

    switch (selectedTab) {
      case 'about':
        return <AboutTab businessData={businessData} isDark={isDark} />;
      case 'products':
        return (
          <ProductsTab
            products={allProducts}
            businessId={businessData.id}
            isDark={isDark}
            loadingMore={loadingMoreProducts}
            hasMore={hasMoreProducts}
            onLoadMore={loadMoreProducts}
          />
        );
      case 'gallery':
        return (
          <GalleryTab
            gallery={cardData?.gallery || []}
            isDark={isDark}
            onImagePress={handleGalleryImagePress}
          />
        );
      case 'reviews':
        return (
          <ReviewsTab
            reviews={allReviews}
            reviewStats={cardData?.reviewStats || null}
            isDark={isDark}
            loadingMore={loadingMoreReviews}
            hasMore={hasMoreReviews}
            onLoadMore={loadMoreReviews}
          />
        );
      default:
        return <AboutTab businessData={businessData} isDark={isDark} />;
    }
  };

  return (
    <View style={styles.container}>
      <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
        {/* Header Section */}
        <PublicCardHeader
          businessData={businessData}
          isDark={isDark}
          onClose={onClose}
        />

        {/* Stats Section */}
        <BusinessStats
          businessData={businessData}
          interactionStatus={interactionStatus}
          isDark={isDark}
        />

        {/* Floating Interaction Buttons */}
        <FloatingInteractionButtons
          interactionStatus={interactionStatus}
          isDark={isDark}
          isOwner={isOwner}
          likeLoading={likeLoading}
          subscribeLoading={subscribeLoading}
          onLikePress={handleLikePress}
          onSubscribePress={handleSubscribePress}
          onReviewPress={() => handleReviewPress(() => setReviewModalVisible(true))}
        />

        {/* Contact Action Buttons */}
        <ContactActionButtons
          businessData={businessData}
          isDark={isDark}
        />

        {/* Social Action Buttons */}
        <SocialActionButtons
          businessData={businessData}
          isDark={isDark}
        />

        {/* Tab Navigation */}
        <TabNavigation
          selectedTab={selectedTab}
          onTabChange={setSelectedTab}
          isDark={isDark}
        />

        {/* Tab Content */}
        {renderTabContent()}
      </ScrollView>

      {/* Review Modal */}
      <ReviewModal
        visible={reviewModalVisible}
        onClose={() => setReviewModalVisible(false)}
        businessId={businessData.id}
        businessName={businessData.business_name}
        existingRating={interactionStatus?.userRating}
        existingReview={interactionStatus?.userReview}
        onReviewSubmitted={handleReviewSubmitted}
      />

      {/* Full Screen Image Viewer */}
      <FullScreenImageViewer
        visible={fullScreenImageVisible}
        images={cardData?.gallery?.map(img => ({ id: img.id, url: img.url })) || []}
        initialIndex={selectedImageIndex}
        onClose={() => setFullScreenImageVisible(false)}
      />
    </View>
  );
}