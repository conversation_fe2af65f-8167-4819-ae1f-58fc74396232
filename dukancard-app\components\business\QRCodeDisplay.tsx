import React from 'react';
import { View, Text, TouchableOpacity, Alert, Linking } from 'react-native';
import { QrCode } from 'lucide-react-native';
import { generateDukancardUrl } from '../../lib/utils/qrCodeUtils';

interface QRCodeDisplayProps {
  businessSlug: string;
  businessName?: string;
  isDark?: boolean;
  size?: number;
  showUrl?: boolean;
  containerStyle?: any;
}

export default function QRCodeDisplay({
  businessSlug,
  businessName = '',
  isDark = false,
  size = 60,
  showUrl = true,
  containerStyle,
}: QRCodeDisplayProps) {
  if (!businessSlug) {
    return (
      <View style={[{
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        gap: 12,
        backgroundColor: isDark ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.05)',
        padding: 10,
        borderRadius: 8,
        borderWidth: 1,
        borderColor: isDark ? 'rgba(255, 255, 255, 0.2)' : 'rgba(0, 0, 0, 0.1)',
      }, containerStyle]}>
        <View style={{ flex: 1, minWidth: 0 }}>
          <Text style={{
            fontSize: 12,
            fontWeight: '500',
            color: isDark ? '#D1D5DB' : '#6B7280',
            marginBottom: 4,
          }}>
            Scan for Dukancard Profile
          </Text>
          <Text style={{
            fontSize: 12,
            fontFamily: 'monospace',
            color: isDark ? '#9CA3AF' : '#9CA3AF',
            lineHeight: 16,
          }}>
            Set Slug to activate
          </Text>
        </View>
        <View style={{
          backgroundColor: '#fff',
          padding: 6,
          borderRadius: 8,
          shadowColor: '#000',
          shadowOffset: { width: 0, height: 2 },
          shadowOpacity: 0.1,
          shadowRadius: 4,
          elevation: 3,
          opacity: 0.5,
        }}>
          <QrCode
            color={isDark ? '#F59E0B' : '#374151'}
            size={size}
            strokeWidth={1.5}
          />
        </View>
      </View>
    );
  }

  const qrValue = generateDukancardUrl(businessSlug);
  const displayUrl = `dukancard.in/${businessSlug}`;

  const handleUrlPress = () => {
    Alert.alert(
      'Open Profile',
      `Would you like to open ${businessName || 'this business'} profile in your browser?`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Open',
          onPress: () => {
            Linking.openURL(qrValue).catch(() => {
              Alert.alert('Error', 'Unable to open the URL');
            });
          },
        },
      ]
    );
  };

  return (
    <View style={[{
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
      gap: 12,
      backgroundColor: isDark ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.05)',
      padding: 10,
      borderRadius: 8,
      borderWidth: 1,
      borderColor: isDark ? 'rgba(255, 255, 255, 0.2)' : 'rgba(0, 0, 0, 0.1)',
    }, containerStyle]}>
      <View style={{ flex: 1, minWidth: 0 }}>
        <Text style={{
          fontSize: 12,
          fontWeight: '500',
          color: isDark ? '#D1D5DB' : '#6B7280',
          marginBottom: 4,
        }}>
          Scan for Dukancard Profile
        </Text>
        {showUrl && (
          <TouchableOpacity onPress={handleUrlPress} activeOpacity={0.7}>
            <Text style={{
              fontSize: 12,
              fontFamily: 'monospace',
              color: '#F59E0B',
              fontWeight: '600',
              lineHeight: 16,
            }} numberOfLines={1}>
              {displayUrl}
            </Text>
          </TouchableOpacity>
        )}
      </View>
      <View style={{
        backgroundColor: '#fff',
        padding: 6,
        borderRadius: 8,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
        elevation: 3,
      }}>
        <QRCode
          value={qrValue}
          size={size}
          color="#000000"
          backgroundColor="#FFFFFF"
          logoSize={0}
          logoMargin={0}
          logoBorderRadius={0}
          quietZone={0}
        />
      </View>
    </View>
  );
}
