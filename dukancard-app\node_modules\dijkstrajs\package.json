{"name": "dijkstrajs", "version": "1.0.3", "description": "A simple JavaScript implementation of Dijkstra's single-source shortest-paths algorithm.", "main": "dijkstra.js", "scripts": {"pretest": "jshint dijkstra.js", "test": "mocha -R spec"}, "repository": {"type": "git", "url": "git://github.com/tcort/dijkstrajs"}, "keywords": ["<PERSON><PERSON><PERSON>", "shortest", "path", "search", "graph"], "license": "MIT", "bugs": {"url": "https://github.com/tcort/dijkstrajs/issues"}, "homepage": "https://github.com/tcort/dijkstrajs", "devDependencies": {"expect.js": "^0.3.1", "jshint": "^2.13.6", "mocha": "^10.2.0"}, "jshintConfig": {"bitwise": true, "curly": true, "eqeqeq": true, "forin": true, "freeze": true, "globalstrict": true, "immed": true, "indent": 4, "moz": true, "newcap": true, "noarg": true, "node": true, "noempty": true, "nonew": true, "trailing": true, "undef": true, "smarttabs": true, "strict": true, "validthis": true, "globals": {"describe": false, "it": false, "before": false, "beforeEach": false, "after": false, "afterEach": false}}}