# qr.js

qrcode encoding in pure javascript with no additional dependencies

```js
var qr = require('qr.js');

var qrcode = qr('foo bar baz');

var cells = qrcode.modules;

// cell[R] -> row
// if cell[R][C] is true, that cell is "on"
```

[interactive example](http://tryme.jit.su/shtylman/qr.js/example)

# credit

This library is repackaged work from the qrcode library here http://www.d-project.com/ which is also licensed under the MIT license. See LICENSE-qrcode for the license file to that project.

# notes
The word "QR Code" is registered trademark of DENSO WAVE INCORPORATED
http://www.denso-wave.com/qrcode/faqpatent-e.html
