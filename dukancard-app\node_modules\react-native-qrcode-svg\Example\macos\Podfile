require_relative '../node_modules/react-native-macos/scripts/react_native_pods'
require_relative '../node_modules/@react-native-community/cli-platform-ios/native_modules'

prepare_react_native_project!

target 'Example-macOS' do
  platform :macos, '10.15'
  use_native_modules!

  # Flags change depending on the env values.
  flags = get_default_flags()

  use_react_native!(
    :path => '../node_modules/react-native-macos',
    :hermes_enabled => false,
    :fabric_enabled => ENV['RCT_NEW_ARCH_ENABLED'] == '1',
    # Flipper is not compatible w/ macOS
    :flipper_configuration => FlipperConfiguration.disabled,
    # An absolute path to your application root.
    :app_path => "#{Pod::Config.instance.installation_root}/.."
  )

  post_install do |installer|
    react_native_post_install(installer)
  end
end
