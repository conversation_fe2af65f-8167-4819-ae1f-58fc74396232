PODS:
  - boost (1.83.0)
  - DoubleConversion (1.1.6)
  - FBLazyVector (0.73.24)
  - FBReactNativeSpec (0.73.24):
    - RCT-Folly (= 2022.05.16.00)
    - RCTRequired (= 0.73.24)
    - RCTTypeSafety (= 0.73.24)
    - React-Core (= 0.73.24)
    - React-jsi (= 0.73.24)
    - ReactCommon/turbomodule/core (= 0.73.24)
  - fmt (9.1.0)
  - glog (0.3.5)
  - RCT-Folly (2022.05.16.00):
    - boost
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - RCT-Folly/Default (= 2022.05.16.00)
  - RCT-Folly/Default (2022.05.16.00):
    - boost
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
  - RCT-Folly/Fabric (2022.05.16.00):
    - boost
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
  - <PERSON>TRequired (0.73.24)
  - RCTTypeSafety (0.73.24):
    - FBLazyVector (= 0.73.24)
    - RCTRequired (= 0.73.24)
    - React-Core (= 0.73.24)
  - React (0.73.24):
    - React-Core (= 0.73.24)
    - React-Core/DevSupport (= 0.73.24)
    - React-Core/RCTWebSocket (= 0.73.24)
    - React-RCTActionSheet (= 0.73.24)
    - React-RCTAnimation (= 0.73.24)
    - React-RCTBlob (= 0.73.24)
    - React-RCTImage (= 0.73.24)
    - React-RCTLinking (= 0.73.24)
    - React-RCTNetwork (= 0.73.24)
    - React-RCTSettings (= 0.73.24)
    - React-RCTText (= 0.73.24)
    - React-RCTVibration (= 0.73.24)
  - React-callinvoker (0.73.24)
  - React-Codegen (0.73.24):
    - DoubleConversion
    - FBReactNativeSpec
    - glog
    - RCT-Folly
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - React-NativeModulesApple
    - React-rncore
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
  - React-Core (0.73.24):
    - glog
    - RCT-Folly (= 2022.05.16.00)
    - React-Core/Default (= 0.73.24)
    - React-cxxreact
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.7.0)
    - Yoga
  - React-Core/CoreModulesHeaders (0.73.24):
    - glog
    - RCT-Folly (= 2022.05.16.00)
    - React-Core/Default
    - React-cxxreact
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.7.0)
    - Yoga
  - React-Core/Default (0.73.24):
    - glog
    - RCT-Folly (= 2022.05.16.00)
    - React-cxxreact
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.7.0)
    - Yoga
  - React-Core/DevSupport (0.73.24):
    - glog
    - RCT-Folly (= 2022.05.16.00)
    - React-Core/Default (= 0.73.24)
    - React-Core/RCTWebSocket (= 0.73.24)
    - React-cxxreact
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector (= 0.73.24)
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.7.0)
    - Yoga
  - React-Core/RCTActionSheetHeaders (0.73.24):
    - glog
    - RCT-Folly (= 2022.05.16.00)
    - React-Core/Default
    - React-cxxreact
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.7.0)
    - Yoga
  - React-Core/RCTAnimationHeaders (0.73.24):
    - glog
    - RCT-Folly (= 2022.05.16.00)
    - React-Core/Default
    - React-cxxreact
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.7.0)
    - Yoga
  - React-Core/RCTBlobHeaders (0.73.24):
    - glog
    - RCT-Folly (= 2022.05.16.00)
    - React-Core/Default
    - React-cxxreact
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.7.0)
    - Yoga
  - React-Core/RCTImageHeaders (0.73.24):
    - glog
    - RCT-Folly (= 2022.05.16.00)
    - React-Core/Default
    - React-cxxreact
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.7.0)
    - Yoga
  - React-Core/RCTLinkingHeaders (0.73.24):
    - glog
    - RCT-Folly (= 2022.05.16.00)
    - React-Core/Default
    - React-cxxreact
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.7.0)
    - Yoga
  - React-Core/RCTNetworkHeaders (0.73.24):
    - glog
    - RCT-Folly (= 2022.05.16.00)
    - React-Core/Default
    - React-cxxreact
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.7.0)
    - Yoga
  - React-Core/RCTSettingsHeaders (0.73.24):
    - glog
    - RCT-Folly (= 2022.05.16.00)
    - React-Core/Default
    - React-cxxreact
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.7.0)
    - Yoga
  - React-Core/RCTTextHeaders (0.73.24):
    - glog
    - RCT-Folly (= 2022.05.16.00)
    - React-Core/Default
    - React-cxxreact
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.7.0)
    - Yoga
  - React-Core/RCTVibrationHeaders (0.73.24):
    - glog
    - RCT-Folly (= 2022.05.16.00)
    - React-Core/Default
    - React-cxxreact
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.7.0)
    - Yoga
  - React-Core/RCTWebSocket (0.73.24):
    - glog
    - RCT-Folly (= 2022.05.16.00)
    - React-Core/Default (= 0.73.24)
    - React-cxxreact
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.7.0)
    - Yoga
  - React-CoreModules (0.73.24):
    - RCT-Folly (= 2022.05.16.00)
    - RCTTypeSafety (= 0.73.24)
    - React-Codegen
    - React-Core/CoreModulesHeaders (= 0.73.24)
    - React-jsi (= 0.73.24)
    - React-NativeModulesApple
    - React-RCTBlob
    - React-RCTImage (= 0.73.24)
    - ReactCommon
    - SocketRocket (= 0.7.0)
  - React-cxxreact (0.73.24):
    - boost (= 1.83.0)
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - RCT-Folly (= 2022.05.16.00)
    - React-callinvoker (= 0.73.24)
    - React-debug (= 0.73.24)
    - React-jsi (= 0.73.24)
    - React-jsinspector (= 0.73.24)
    - React-logger (= 0.73.24)
    - React-perflogger (= 0.73.24)
    - React-runtimeexecutor (= 0.73.24)
  - React-debug (0.73.24)
  - React-Fabric (0.73.24):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric/animations (= 0.73.24)
    - React-Fabric/attributedstring (= 0.73.24)
    - React-Fabric/componentregistry (= 0.73.24)
    - React-Fabric/componentregistrynative (= 0.73.24)
    - React-Fabric/components (= 0.73.24)
    - React-Fabric/core (= 0.73.24)
    - React-Fabric/imagemanager (= 0.73.24)
    - React-Fabric/leakchecker (= 0.73.24)
    - React-Fabric/mounting (= 0.73.24)
    - React-Fabric/scheduler (= 0.73.24)
    - React-Fabric/telemetry (= 0.73.24)
    - React-Fabric/templateprocessor (= 0.73.24)
    - React-Fabric/textlayoutmanager (= 0.73.24)
    - React-Fabric/uimanager (= 0.73.24)
    - React-graphics
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/animations (0.73.24):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/attributedstring (0.73.24):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/componentregistry (0.73.24):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/componentregistrynative (0.73.24):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/components (0.73.24):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric/components/inputaccessory (= 0.73.24)
    - React-Fabric/components/legacyviewmanagerinterop (= 0.73.24)
    - React-Fabric/components/modal (= 0.73.24)
    - React-Fabric/components/rncore (= 0.73.24)
    - React-Fabric/components/root (= 0.73.24)
    - React-Fabric/components/safeareaview (= 0.73.24)
    - React-Fabric/components/scrollview (= 0.73.24)
    - React-Fabric/components/text (= 0.73.24)
    - React-Fabric/components/textinput (= 0.73.24)
    - React-Fabric/components/unimplementedview (= 0.73.24)
    - React-Fabric/components/view (= 0.73.24)
    - React-graphics
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/components/inputaccessory (0.73.24):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/components/legacyviewmanagerinterop (0.73.24):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/components/modal (0.73.24):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/components/rncore (0.73.24):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/components/root (0.73.24):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/components/safeareaview (0.73.24):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/components/scrollview (0.73.24):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/components/text (0.73.24):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/components/textinput (0.73.24):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/components/unimplementedview (0.73.24):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/components/view (0.73.24):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - Yoga
  - React-Fabric/core (0.73.24):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/imagemanager (0.73.24):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/leakchecker (0.73.24):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/mounting (0.73.24):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/scheduler (0.73.24):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/telemetry (0.73.24):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/templateprocessor (0.73.24):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/textlayoutmanager (0.73.24):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric/uimanager
    - React-graphics
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/uimanager (0.73.24):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-graphics
    - React-jsc
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-FabricImage (0.73.24):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - RCTRequired (= 0.73.24)
    - RCTTypeSafety (= 0.73.24)
    - React-Fabric
    - React-graphics
    - React-ImageManager
    - React-jsc
    - React-jsi
    - React-jsiexecutor (= 0.73.24)
    - React-logger
    - React-rendererdebug
    - React-utils
    - ReactCommon
    - Yoga
  - React-graphics (0.73.24):
    - glog
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - React-Core/Default (= 0.73.24)
    - React-utils
  - React-ImageManager (0.73.24):
    - glog
    - RCT-Folly/Fabric
    - React-Core/Default
    - React-debug
    - React-Fabric
    - React-graphics
    - React-rendererdebug
    - React-utils
  - React-jsc (0.73.24):
    - React-jsc/Fabric (= 0.73.24)
    - React-jsi (= 0.73.24)
  - React-jsc/Fabric (0.73.24):
    - React-jsi (= 0.73.24)
  - React-jserrorhandler (0.73.24):
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - React-debug
    - React-jsi
    - React-Mapbuffer
  - React-jsi (0.73.24):
    - boost (= 1.83.0)
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - RCT-Folly (= 2022.05.16.00)
  - React-jsiexecutor (0.73.24):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - RCT-Folly (= 2022.05.16.00)
    - React-cxxreact (= 0.73.24)
    - React-jsi (= 0.73.24)
    - React-perflogger (= 0.73.24)
  - React-jsinspector (0.73.24)
  - React-logger (0.73.24):
    - glog
  - React-Mapbuffer (0.73.24):
    - glog
    - React-debug
  - React-nativeconfig (0.73.24)
  - React-NativeModulesApple (0.73.24):
    - glog
    - React-callinvoker
    - React-Core
    - React-cxxreact
    - React-jsc
    - React-jsi
    - React-runtimeexecutor
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
  - React-perflogger (0.73.24)
  - React-RCTActionSheet (0.73.24):
    - React-Core/RCTActionSheetHeaders (= 0.73.24)
  - React-RCTAnimation (0.73.24):
    - RCT-Folly (= 2022.05.16.00)
    - RCTTypeSafety
    - React-Codegen
    - React-Core/RCTAnimationHeaders
    - React-jsi
    - React-NativeModulesApple
    - ReactCommon
  - React-RCTAppDelegate (0.73.24):
    - RCT-Folly
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-CoreModules
    - React-jsc
    - React-nativeconfig
    - React-NativeModulesApple
    - React-RCTFabric
    - React-RCTImage
    - React-RCTNetwork
    - React-runtimescheduler
    - ReactCommon
  - React-RCTBlob (0.73.24):
    - RCT-Folly (= 2022.05.16.00)
    - React-Codegen
    - React-Core/RCTBlobHeaders
    - React-Core/RCTWebSocket
    - React-jsi
    - React-NativeModulesApple
    - React-RCTNetwork
    - ReactCommon
  - React-RCTFabric (0.73.24):
    - glog
    - RCT-Folly/Fabric (= 2022.05.16.00)
    - React-Core
    - React-debug
    - React-Fabric
    - React-FabricImage
    - React-graphics
    - React-ImageManager
    - React-jsc
    - React-jsi
    - React-nativeconfig
    - React-RCTImage
    - React-RCTText
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - Yoga
  - React-RCTImage (0.73.24):
    - RCT-Folly (= 2022.05.16.00)
    - RCTTypeSafety
    - React-Codegen
    - React-Core/RCTImageHeaders
    - React-jsi
    - React-NativeModulesApple
    - React-RCTNetwork
    - ReactCommon
  - React-RCTLinking (0.73.24):
    - React-Codegen
    - React-Core/RCTLinkingHeaders (= 0.73.24)
    - React-jsi (= 0.73.24)
    - React-NativeModulesApple
    - ReactCommon
    - ReactCommon/turbomodule/core (= 0.73.24)
  - React-RCTNetwork (0.73.24):
    - RCT-Folly (= 2022.05.16.00)
    - RCTTypeSafety
    - React-Codegen
    - React-Core/RCTNetworkHeaders
    - React-jsi
    - React-NativeModulesApple
    - ReactCommon
  - React-RCTSettings (0.73.24):
    - RCT-Folly (= 2022.05.16.00)
    - RCTTypeSafety
    - React-Codegen
    - React-Core/RCTSettingsHeaders
    - React-jsi
    - React-NativeModulesApple
    - ReactCommon
  - React-RCTText (0.73.24):
    - React-Core/RCTTextHeaders (= 0.73.24)
    - Yoga
  - React-RCTVibration (0.73.24):
    - RCT-Folly (= 2022.05.16.00)
    - React-Codegen
    - React-Core/RCTVibrationHeaders
    - React-jsi
    - React-NativeModulesApple
    - ReactCommon
  - React-rendererdebug (0.73.24):
    - DoubleConversion
    - fmt (= 9.1.0)
    - RCT-Folly (= 2022.05.16.00)
    - React-debug
  - React-rncore (0.73.24)
  - React-runtimeexecutor (0.73.24):
    - React-jsi (= 0.73.24)
  - React-runtimescheduler (0.73.24):
    - glog
    - RCT-Folly (= 2022.05.16.00)
    - React-callinvoker
    - React-cxxreact
    - React-debug
    - React-jsc
    - React-jsi
    - React-rendererdebug
    - React-runtimeexecutor
    - React-utils
  - React-utils (0.73.24):
    - glog
    - RCT-Folly (= 2022.05.16.00)
    - React-debug
  - ReactCommon (0.73.24):
    - React-logger (= 0.73.24)
    - ReactCommon/turbomodule (= 0.73.24)
  - ReactCommon/turbomodule (0.73.24):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - RCT-Folly (= 2022.05.16.00)
    - React-callinvoker (= 0.73.24)
    - React-cxxreact (= 0.73.24)
    - React-jsi (= 0.73.24)
    - React-logger (= 0.73.24)
    - React-perflogger (= 0.73.24)
    - ReactCommon/turbomodule/bridging (= 0.73.24)
    - ReactCommon/turbomodule/core (= 0.73.24)
  - ReactCommon/turbomodule/bridging (0.73.24):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - RCT-Folly (= 2022.05.16.00)
    - React-callinvoker (= 0.73.24)
    - React-cxxreact (= 0.73.24)
    - React-jsi (= 0.73.24)
    - React-logger (= 0.73.24)
    - React-perflogger (= 0.73.24)
  - ReactCommon/turbomodule/core (0.73.24):
    - DoubleConversion
    - fmt (= 9.1.0)
    - glog
    - RCT-Folly (= 2022.05.16.00)
    - React-callinvoker (= 0.73.24)
    - React-cxxreact (= 0.73.24)
    - React-jsi (= 0.73.24)
    - React-logger (= 0.73.24)
    - React-perflogger (= 0.73.24)
  - RNSVG (15.1.0):
    - React-Core
  - SocketRocket (0.7.0)
  - Yoga (1.14.0)

DEPENDENCIES:
  - boost (from `../node_modules/react-native-macos/third-party-podspecs/boost.podspec`)
  - DoubleConversion (from `../node_modules/react-native-macos/third-party-podspecs/DoubleConversion.podspec`)
  - FBLazyVector (from `../node_modules/react-native-macos/Libraries/FBLazyVector`)
  - FBReactNativeSpec (from `../node_modules/react-native-macos/React/FBReactNativeSpec`)
  - fmt (from `../node_modules/react-native-macos/third-party-podspecs/fmt.podspec`)
  - glog (from `../node_modules/react-native-macos/third-party-podspecs/glog.podspec`)
  - RCT-Folly (from `../node_modules/react-native-macos/third-party-podspecs/RCT-Folly.podspec`)
  - RCT-Folly/Fabric (from `../node_modules/react-native-macos/third-party-podspecs/RCT-Folly.podspec`)
  - RCTRequired (from `../node_modules/react-native-macos/Libraries/RCTRequired`)
  - RCTTypeSafety (from `../node_modules/react-native-macos/Libraries/TypeSafety`)
  - React (from `../node_modules/react-native-macos/`)
  - React-callinvoker (from `../node_modules/react-native-macos/ReactCommon/callinvoker`)
  - React-Codegen (from `build/generated/ios`)
  - React-Core (from `../node_modules/react-native-macos/`)
  - React-Core/RCTWebSocket (from `../node_modules/react-native-macos/`)
  - React-CoreModules (from `../node_modules/react-native-macos/React/CoreModules`)
  - React-cxxreact (from `../node_modules/react-native-macos/ReactCommon/cxxreact`)
  - React-debug (from `../node_modules/react-native-macos/ReactCommon/react/debug`)
  - React-Fabric (from `../node_modules/react-native-macos/ReactCommon`)
  - React-FabricImage (from `../node_modules/react-native-macos/ReactCommon`)
  - React-graphics (from `../node_modules/react-native-macos/ReactCommon/react/renderer/graphics`)
  - React-ImageManager (from `../node_modules/react-native-macos/ReactCommon/react/renderer/imagemanager/platform/ios`)
  - React-jsc (from `../node_modules/react-native-macos/ReactCommon/jsc`)
  - React-jserrorhandler (from `../node_modules/react-native-macos/ReactCommon/jserrorhandler`)
  - React-jsi (from `../node_modules/react-native-macos/ReactCommon/jsi`)
  - React-jsiexecutor (from `../node_modules/react-native-macos/ReactCommon/jsiexecutor`)
  - React-jsinspector (from `../node_modules/react-native-macos/ReactCommon/jsinspector-modern`)
  - React-logger (from `../node_modules/react-native-macos/ReactCommon/logger`)
  - React-Mapbuffer (from `../node_modules/react-native-macos/ReactCommon`)
  - React-nativeconfig (from `../node_modules/react-native-macos/ReactCommon`)
  - React-NativeModulesApple (from `../node_modules/react-native-macos/ReactCommon/react/nativemodule/core/platform/ios`)
  - React-perflogger (from `../node_modules/react-native-macos/ReactCommon/reactperflogger`)
  - React-RCTActionSheet (from `../node_modules/react-native-macos/Libraries/ActionSheetIOS`)
  - React-RCTAnimation (from `../node_modules/react-native-macos/Libraries/NativeAnimation`)
  - React-RCTAppDelegate (from `../node_modules/react-native-macos/Libraries/AppDelegate`)
  - React-RCTBlob (from `../node_modules/react-native-macos/Libraries/Blob`)
  - React-RCTFabric (from `../node_modules/react-native-macos/React`)
  - React-RCTImage (from `../node_modules/react-native-macos/Libraries/Image`)
  - React-RCTLinking (from `../node_modules/react-native-macos/Libraries/LinkingIOS`)
  - React-RCTNetwork (from `../node_modules/react-native-macos/Libraries/Network`)
  - React-RCTSettings (from `../node_modules/react-native-macos/Libraries/Settings`)
  - React-RCTText (from `../node_modules/react-native-macos/Libraries/Text`)
  - React-RCTVibration (from `../node_modules/react-native-macos/Libraries/Vibration`)
  - React-rendererdebug (from `../node_modules/react-native-macos/ReactCommon/react/renderer/debug`)
  - React-rncore (from `../node_modules/react-native-macos/ReactCommon`)
  - React-runtimeexecutor (from `../node_modules/react-native-macos/ReactCommon/runtimeexecutor`)
  - React-runtimescheduler (from `../node_modules/react-native-macos/ReactCommon/react/renderer/runtimescheduler`)
  - React-utils (from `../node_modules/react-native-macos/ReactCommon/react/utils`)
  - ReactCommon/turbomodule/core (from `../node_modules/react-native-macos/ReactCommon`)
  - RNSVG (from `../node_modules/react-native-svg`)
  - SocketRocket (from `../node_modules/react-native-macos/third-party-podspecs/SocketRocket.podspec`)
  - Yoga (from `../node_modules/react-native-macos/ReactCommon/yoga`)

EXTERNAL SOURCES:
  boost:
    :podspec: "../node_modules/react-native-macos/third-party-podspecs/boost.podspec"
  DoubleConversion:
    :podspec: "../node_modules/react-native-macos/third-party-podspecs/DoubleConversion.podspec"
  FBLazyVector:
    :path: "../node_modules/react-native-macos/Libraries/FBLazyVector"
  FBReactNativeSpec:
    :path: "../node_modules/react-native-macos/React/FBReactNativeSpec"
  fmt:
    :podspec: "../node_modules/react-native-macos/third-party-podspecs/fmt.podspec"
  glog:
    :podspec: "../node_modules/react-native-macos/third-party-podspecs/glog.podspec"
  RCT-Folly:
    :podspec: "../node_modules/react-native-macos/third-party-podspecs/RCT-Folly.podspec"
  RCTRequired:
    :path: "../node_modules/react-native-macos/Libraries/RCTRequired"
  RCTTypeSafety:
    :path: "../node_modules/react-native-macos/Libraries/TypeSafety"
  React:
    :path: "../node_modules/react-native-macos/"
  React-callinvoker:
    :path: "../node_modules/react-native-macos/ReactCommon/callinvoker"
  React-Codegen:
    :path: build/generated/ios
  React-Core:
    :path: "../node_modules/react-native-macos/"
  React-CoreModules:
    :path: "../node_modules/react-native-macos/React/CoreModules"
  React-cxxreact:
    :path: "../node_modules/react-native-macos/ReactCommon/cxxreact"
  React-debug:
    :path: "../node_modules/react-native-macos/ReactCommon/react/debug"
  React-Fabric:
    :path: "../node_modules/react-native-macos/ReactCommon"
  React-FabricImage:
    :path: "../node_modules/react-native-macos/ReactCommon"
  React-graphics:
    :path: "../node_modules/react-native-macos/ReactCommon/react/renderer/graphics"
  React-ImageManager:
    :path: "../node_modules/react-native-macos/ReactCommon/react/renderer/imagemanager/platform/ios"
  React-jsc:
    :path: "../node_modules/react-native-macos/ReactCommon/jsc"
  React-jserrorhandler:
    :path: "../node_modules/react-native-macos/ReactCommon/jserrorhandler"
  React-jsi:
    :path: "../node_modules/react-native-macos/ReactCommon/jsi"
  React-jsiexecutor:
    :path: "../node_modules/react-native-macos/ReactCommon/jsiexecutor"
  React-jsinspector:
    :path: "../node_modules/react-native-macos/ReactCommon/jsinspector-modern"
  React-logger:
    :path: "../node_modules/react-native-macos/ReactCommon/logger"
  React-Mapbuffer:
    :path: "../node_modules/react-native-macos/ReactCommon"
  React-nativeconfig:
    :path: "../node_modules/react-native-macos/ReactCommon"
  React-NativeModulesApple:
    :path: "../node_modules/react-native-macos/ReactCommon/react/nativemodule/core/platform/ios"
  React-perflogger:
    :path: "../node_modules/react-native-macos/ReactCommon/reactperflogger"
  React-RCTActionSheet:
    :path: "../node_modules/react-native-macos/Libraries/ActionSheetIOS"
  React-RCTAnimation:
    :path: "../node_modules/react-native-macos/Libraries/NativeAnimation"
  React-RCTAppDelegate:
    :path: "../node_modules/react-native-macos/Libraries/AppDelegate"
  React-RCTBlob:
    :path: "../node_modules/react-native-macos/Libraries/Blob"
  React-RCTFabric:
    :path: "../node_modules/react-native-macos/React"
  React-RCTImage:
    :path: "../node_modules/react-native-macos/Libraries/Image"
  React-RCTLinking:
    :path: "../node_modules/react-native-macos/Libraries/LinkingIOS"
  React-RCTNetwork:
    :path: "../node_modules/react-native-macos/Libraries/Network"
  React-RCTSettings:
    :path: "../node_modules/react-native-macos/Libraries/Settings"
  React-RCTText:
    :path: "../node_modules/react-native-macos/Libraries/Text"
  React-RCTVibration:
    :path: "../node_modules/react-native-macos/Libraries/Vibration"
  React-rendererdebug:
    :path: "../node_modules/react-native-macos/ReactCommon/react/renderer/debug"
  React-rncore:
    :path: "../node_modules/react-native-macos/ReactCommon"
  React-runtimeexecutor:
    :path: "../node_modules/react-native-macos/ReactCommon/runtimeexecutor"
  React-runtimescheduler:
    :path: "../node_modules/react-native-macos/ReactCommon/react/renderer/runtimescheduler"
  React-utils:
    :path: "../node_modules/react-native-macos/ReactCommon/react/utils"
  ReactCommon:
    :path: "../node_modules/react-native-macos/ReactCommon"
  RNSVG:
    :path: "../node_modules/react-native-svg"
  SocketRocket:
    :podspec: "../node_modules/react-native-macos/third-party-podspecs/SocketRocket.podspec"
  Yoga:
    :path: "../node_modules/react-native-macos/ReactCommon/yoga"

SPEC CHECKSUMS:
  boost: 0686b6af8cbd638c784fea5afb789be66699823c
  DoubleConversion: ca54355f8932558971f6643521d62b9bc8231cee
  FBLazyVector: 6d4868013ba47ee362b596a3ccc6f256a0412519
  FBReactNativeSpec: 030e69b320c718834248085bfd0bd6652141c28c
  fmt: 03574da4b7ba40de39da59677ca66610ce8c4a02
  glog: 3a72874c0322c7caf24931d3a2777cb7a3090529
  RCT-Folly: 68e9c0fd4c0f05964afd447041d3ac2d67298f27
  RCTRequired: 80f6978c000be199cc243f876974fd848651a247
  RCTTypeSafety: cf3c24b03624d8ed3b7a1723f25ac159ff19ad70
  React: fb69001383ecdd0ebb7c9282acadf970ca1a3e88
  React-callinvoker: c8f46ba759794eff6f17f4f6f30c5ae0839dca0b
  React-Codegen: 3253e71b9da6466c30701d5cbf777d28b1f7ef86
  React-Core: c6602cf0bb3e630f9c683235dc0e3745acda5537
  React-CoreModules: 8b0b6ef6876ff450c182919f9914072e8942e7e4
  React-cxxreact: f7d01785333d0f1db4d2dbe86cf7ea3d27336d4e
  React-debug: 509056a30640ee068c5543f75638dd5b96c93910
  React-Fabric: 6886e037fbaf896c8f1b0d0472b157e3b5bc9f3e
  React-FabricImage: a4616d0fd248f59f78c005d4818aeb5cff159c62
  React-graphics: 61973f4cee610ab9a787da2feeef9cc049ea9c43
  React-ImageManager: 872c1961a4ab1f42641b71ca7ae6396795875c50
  React-jsc: dcd5be1fc922f441a41211bd69090cf84ab939c5
  React-jserrorhandler: 307fc7495c7aedb2758155c564d688740c9b24e3
  React-jsi: 3242c04ef526d635627202454f9ff14310de36d2
  React-jsiexecutor: 7033e5015a768c45dc8586cd4f568175e0e73523
  React-jsinspector: 4fd38f54e53e1695f800dd520e9ca7d4fa21d912
  React-logger: 2d01c93a8547acabafe19df4f98055a05b178c45
  React-Mapbuffer: ff9c7733ae0b3036613af2d973d6818a0a8763dc
  React-nativeconfig: b865de30cded2e6a24f248514b8962d7ee070de5
  React-NativeModulesApple: 4ad0827e367838e83613c46cda9bff6a74acb908
  React-perflogger: 93d744092a0c25cc7edd34babef619c180ca89b5
  React-RCTActionSheet: 1a343c3a1d33995e24bad5571685cb5b44312a1a
  React-RCTAnimation: cbcab7290b989e04fadc26c32e4bf123e0840bf8
  React-RCTAppDelegate: 07e70022fa0d4b8dd2d6e0495284a47526da9abc
  React-RCTBlob: f529fba669a1ee229fdd45c087fa012f363aa25b
  React-RCTFabric: 0e04ff2234a6b3e33e7223eda2a29a1aabfbe4d2
  React-RCTImage: 6f1fcb7fe428931a9974ddc24e4e9e11f0ec67fc
  React-RCTLinking: 8628ae06b6e3b7e06c624454419afe1f11e700b4
  React-RCTNetwork: 2938f1ec54273d3f205ee75493efb3ac6c479598
  React-RCTSettings: 15084f505e72ccb50a93395212660dca1b9651be
  React-RCTText: c995f61d1391a6c773efd84108f2d20f26d11019
  React-RCTVibration: 7632c700a179cd58e3fd08a6464b04a81ae78616
  React-rendererdebug: e8ea49e9eefd0d600479f2cd72d689134fcb8360
  React-rncore: 73dee69ccf53fc8cf891b08e1766ad73ab402f9a
  React-runtimeexecutor: 1fd45f1e0f2faadd2db6176710af89e01115c704
  React-runtimescheduler: 265991752129c54256f223defa0272509fe81ad7
  React-utils: fad5a988b6e23d8b98a4450408bfcc56d63ac85c
  ReactCommon: 67c1d3a5dafe396de7d497fe224c12217f8b6f49
  RNSVG: 50cf2c7018e57cf5d3522d98d0a3a4dd6bf9d093
  SocketRocket: f6c6249082c011e6de2de60ed641ef8bbe0cfac9
  Yoga: 24ddb4963e2a3330df762423f0a9de2096325401

PODFILE CHECKSUM: 8314a989ac1c9965ae099e2dde80ba249c2fd077

COCOAPODS: 1.15.2
