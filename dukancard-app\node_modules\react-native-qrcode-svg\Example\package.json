{"name": "Example", "version": "0.0.1", "private": true, "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "web": "webpack serve", "macos": "react-native run-macos", "lint": "eslint .", "prettier": "prettier --write ./src/**/*.{tsx,ts}", "start": "react-native start", "test": "jest", "tsc": "tsc"}, "dependencies": {"@babel/plugin-proposal-export-namespace-from": "^7.18.9", "@svgr/webpack": "^8.1.0", "file-loader": "^6.2.0", "react": "18.3.1", "react-native": "^0.75.4", "react-native-macos": "^0.75.4", "react-native-qrcode-svg": "link:../", "react-native-svg": "^15.10.1", "react-native-web": "^0.19.10", "webpack": "^5.91.0"}, "devDependencies": {"@babel/core": "^7.20.0", "@babel/preset-env": "^7.20.0", "@babel/runtime": "^7.25.4", "@react-native/babel-preset": "^0.75.4", "@react-native/eslint-config": "0.75.4", "@react-native/metro-config": "0.75.4", "@react-native/typescript-config": "0.75.4", "@types/react": "^18.2.6", "@types/react-test-renderer": "^18.0.0", "babel-jest": "^29.6.3", "babel-loader": "^9.1.3", "eslint": "^8.19.0", "eslint-plugin-prettier": "^5.1.3", "jest": "^29.6.3", "prettier": "^3.2.5", "react-dom": "^18.2.0", "react-test-renderer": "18.2.0", "typescript": "5.0.4", "webpack-cli": "^5.1.4", "webpack-dev-server": "^5.0.4"}, "engines": {"node": ">=18"}}