{"name": "react-native-qrcode-svg", "version": "6.3.15", "description": "A QR Code generator for React Native based on react-native-svg and javascript-qrcode.", "main": "index.js", "types": "index.d.ts", "scripts": {"prepack": "rm -rf example", "test": "jest", "test:update": "npm run test -- --updateSnapshot", "lint": "standard 'src/**/*.js'", "lint:fix": "standard 'src/**/*.js' --fix", "lint:staged": "lint-staged", "check:update": "npx npm-check-updates -u", "check:outdated": "npm outdated"}, "standard": {"env": ["jest"], "parser": "babel-es<PERSON>"}, "jest": {"testPathIgnorePatterns": ["/node_modules/", "example/"]}, "repository": {"type": "git", "url": "git+https://github.com/Expensify/react-native-qrcode-svg.git"}, "files": ["Example", "screenshot", "src", "babel.config.js", "index.d.ts", "index.js", "textEncodingTransformation.js"], "keywords": ["react-native", "qrcode", "svg"], "author": "Expensify", "license": "MIT", "bugs": {"url": "https://github.com/Expensify/react-native-qrcode-svg/issues"}, "homepage": "https://github.com/Expensify/react-native-qrcode-svg#readme", "peerDependencies": {"react": "*", "react-native": ">=0.63.4", "react-native-svg": ">=14.0.0"}, "dependencies": {"prop-types": "^15.8.0", "qrcode": "^1.5.4", "text-encoding": "^0.7.0"}, "devDependencies": {"@types/react": "^18.2.75", "babel-eslint": "^10.1.0", "babel-jest": "^27.4.2", "husky": "^7.0.4", "jest": "^27.4.3", "lint-staged": "^12.1.2", "metro-react-native-babel-preset": "^0.66.2", "react": "^18.1.0", "react-test-renderer": "^18.1.0", "standard": "^16.0.4"}}