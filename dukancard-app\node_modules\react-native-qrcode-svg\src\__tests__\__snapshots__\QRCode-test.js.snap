// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`QRCode renders correctly 1`] = `
<div>
  <span>
    Defs: 
    {}
    children: 
    <span>
      LinearGradient: 
      {"id":"grad","x1":"0%","y1":"0%","x2":"100%","y2":"100%"}
      children: 
      <span>
        Stop: 
        {"offset":"0","stopColor":"rgb(255,0,0)","stopOpacity":"1"}
      </span>
      <span>
        Stop: 
        {"offset":"1","stopColor":"rgb(0,255,255)","stopOpacity":"1"}
      </span>
    </span>
  </span>
  <span>
    G: 
    {}
    children: 
    <span>
      Rect: 
      {"x":0,"y":0,"width":100,"height":100,"fill":"white"}
    </span>
  </span>
  <span>
    G: 
    {}
    children: 
    <span>
      Path: 
      {"d":"M0 2 L28 2 M48 2 L60 2 M64 2 L68 2 M72 2 L100 2 M0 6 L4 6 M24 6 L28 6 M32 6 L52 6 M56 6 L68 6 M72 6 L76 6 M96 6 L100 6 M0 10 L4 10 M8 10 L20 10 M24 10 L28 10 M36 10 L40 10 M48 10 L60 10 M72 10 L76 10 M80 10 L92 10 M96 10 L100 10 M0 14 L4 14 M8 14 L20 14 M24 14 L28 14 M36 14 L40 14 M52 14 L64 14 M72 14 L76 14 M80 14 L92 14 M96 14 L100 14 M0 18 L4 18 M8 18 L20 18 M24 18 L28 18 M32 18 L36 18 M48 18 L52 18 M56 18 L64 18 M72 18 L76 18 M80 18 L92 18 M96 18 L100 18 M0 22 L4 22 M24 22 L28 22 M36 22 L40 22 M44 22 L48 22 M52 22 L56 22 M60 22 L64 22 M72 22 L76 22 M96 22 L100 22 M0 26 L28 26 M32 26 L36 26 M40 26 L44 26 M48 26 L52 26 M56 26 L60 26 M64 26 L68 26 M72 26 L100 26 M40 30 L44 30 M48 30 L68 30 M0 34 L4 34 M8 34 L12 34 M16 34 L20 34 M24 34 L28 34 M36 34 L40 34 M44 34 L48 34 M56 34 L60 34 M64 34 L68 34 M80 34 L84 34 M92 34 L96 34 M4 38 L8 38 M20 38 L24 38 M36 38 L44 38 M48 38 L52 38 M56 38 L60 38 M64 38 L80 38 M84 38 L100 38 M4 42 L8 42 M12 42 L20 42 M24 42 L28 42 M32 42 L36 42 M48 42 L56 42 M72 42 L100 42 M0 46 L24 46 M28 46 L32 46 M36 46 L44 46 M48 46 L52 46 M64 46 L72 46 M76 46 L80 46 M92 46 L96 46 M20 50 L40 50 M48 50 L52 50 M60 50 L76 50 M84 50 L88 50 M4 54 L24 54 M28 54 L32 54 M36 54 L40 54 M44 54 L56 54 M64 54 L68 54 M84 54 L88 54 M92 54 L100 54 M0 58 L4 58 M12 58 L32 58 M36 58 L40 58 M44 58 L52 58 M56 58 L60 58 M72 58 L76 58 M80 58 L88 58 M92 58 L100 58 M4 62 L24 62 M32 62 L44 62 M48 62 L60 62 M76 62 L80 62 M84 62 L88 62 M92 62 L96 62 M0 66 L4 66 M12 66 L16 66 M24 66 L32 66 M36 66 L40 66 M44 66 L48 66 M56 66 L60 66 M64 66 L84 66 M92 66 L100 66 M32 70 L40 70 M44 70 L48 70 M56 70 L68 70 M80 70 L88 70 M96 70 L100 70 M0 74 L28 74 M36 74 L44 74 M52 74 L56 74 M60 74 L68 74 M72 74 L76 74 M80 74 L84 74 M88 74 L100 74 M0 78 L4 78 M24 78 L28 78 M36 78 L40 78 M44 78 L48 78 M64 78 L68 78 M80 78 L84 78 M0 82 L4 82 M8 82 L20 82 M24 82 L28 82 M32 82 L44 82 M48 82 L52 82 M64 82 L84 82 M96 82 L100 82 M0 86 L4 86 M8 86 L20 86 M24 86 L28 86 M36 86 L40 86 M44 86 L56 86 M60 86 L64 86 M76 86 L92 86 M0 90 L4 90 M8 90 L20 90 M24 90 L28 90 M32 90 L36 90 M40 90 L52 90 M56 90 L60 90 M64 90 L76 90 M80 90 L92 90 M96 90 L100 90 M0 94 L4 94 M24 94 L28 94 M48 94 L72 94 M80 94 L84 94 M92 94 L96 94 M0 98 L28 98 M32 98 L48 98 M56 98 L60 98 M64 98 L68 98 M80 98 L88 98 M92 98 L100 98 ","strokeLinecap":"butt","stroke":"black","strokeWidth":4}
    </span>
  </span>
</div>
`;

exports[`QRCode renders with logo correctly 1`] = `
<div>
  <span>
    Defs: 
    {}
    children: 
    <span>
      LinearGradient: 
      {"id":"grad","x1":"0%","y1":"0%","x2":"100%","y2":"100%"}
      children: 
      <span>
        Stop: 
        {"offset":"0","stopColor":"rgb(255,0,0)","stopOpacity":"1"}
      </span>
      <span>
        Stop: 
        {"offset":"1","stopColor":"rgb(0,255,255)","stopOpacity":"1"}
      </span>
    </span>
  </span>
  <span>
    G: 
    {}
    children: 
    <span>
      Rect: 
      {"x":0,"y":0,"width":100,"height":100,"fill":"white"}
    </span>
  </span>
  <span>
    G: 
    {}
    children: 
    <span>
      Path: 
      {"d":"M0 2 L28 2 M48 2 L60 2 M64 2 L68 2 M72 2 L100 2 M0 6 L4 6 M24 6 L28 6 M32 6 L52 6 M56 6 L68 6 M72 6 L76 6 M96 6 L100 6 M0 10 L4 10 M8 10 L20 10 M24 10 L28 10 M36 10 L40 10 M48 10 L60 10 M72 10 L76 10 M80 10 L92 10 M96 10 L100 10 M0 14 L4 14 M8 14 L20 14 M24 14 L28 14 M36 14 L40 14 M52 14 L64 14 M72 14 L76 14 M80 14 L92 14 M96 14 L100 14 M0 18 L4 18 M8 18 L20 18 M24 18 L28 18 M32 18 L36 18 M48 18 L52 18 M56 18 L64 18 M72 18 L76 18 M80 18 L92 18 M96 18 L100 18 M0 22 L4 22 M24 22 L28 22 M36 22 L40 22 M44 22 L48 22 M52 22 L56 22 M60 22 L64 22 M72 22 L76 22 M96 22 L100 22 M0 26 L28 26 M32 26 L36 26 M40 26 L44 26 M48 26 L52 26 M56 26 L60 26 M64 26 L68 26 M72 26 L100 26 M40 30 L44 30 M48 30 L68 30 M0 34 L4 34 M8 34 L12 34 M16 34 L20 34 M24 34 L28 34 M36 34 L40 34 M44 34 L48 34 M56 34 L60 34 M64 34 L68 34 M80 34 L84 34 M92 34 L96 34 M4 38 L8 38 M20 38 L24 38 M36 38 L44 38 M48 38 L52 38 M56 38 L60 38 M64 38 L80 38 M84 38 L100 38 M4 42 L8 42 M12 42 L20 42 M24 42 L28 42 M32 42 L36 42 M48 42 L56 42 M72 42 L100 42 M0 46 L24 46 M28 46 L32 46 M36 46 L44 46 M48 46 L52 46 M64 46 L72 46 M76 46 L80 46 M92 46 L96 46 M20 50 L40 50 M48 50 L52 50 M60 50 L76 50 M84 50 L88 50 M4 54 L24 54 M28 54 L32 54 M36 54 L40 54 M44 54 L56 54 M64 54 L68 54 M84 54 L88 54 M92 54 L100 54 M0 58 L4 58 M12 58 L32 58 M36 58 L40 58 M44 58 L52 58 M56 58 L60 58 M72 58 L76 58 M80 58 L88 58 M92 58 L100 58 M4 62 L24 62 M32 62 L44 62 M48 62 L60 62 M76 62 L80 62 M84 62 L88 62 M92 62 L96 62 M0 66 L4 66 M12 66 L16 66 M24 66 L32 66 M36 66 L40 66 M44 66 L48 66 M56 66 L60 66 M64 66 L84 66 M92 66 L100 66 M32 70 L40 70 M44 70 L48 70 M56 70 L68 70 M80 70 L88 70 M96 70 L100 70 M0 74 L28 74 M36 74 L44 74 M52 74 L56 74 M60 74 L68 74 M72 74 L76 74 M80 74 L84 74 M88 74 L100 74 M0 78 L4 78 M24 78 L28 78 M36 78 L40 78 M44 78 L48 78 M64 78 L68 78 M80 78 L84 78 M0 82 L4 82 M8 82 L20 82 M24 82 L28 82 M32 82 L44 82 M48 82 L52 82 M64 82 L84 82 M96 82 L100 82 M0 86 L4 86 M8 86 L20 86 M24 86 L28 86 M36 86 L40 86 M44 86 L56 86 M60 86 L64 86 M76 86 L92 86 M0 90 L4 90 M8 90 L20 90 M24 90 L28 90 M32 90 L36 90 M40 90 L52 90 M56 90 L60 90 M64 90 L76 90 M80 90 L92 90 M96 90 L100 90 M0 94 L4 94 M24 94 L28 94 M48 94 L72 94 M80 94 L84 94 M92 94 L96 94 M0 98 L28 98 M32 98 L48 98 M56 98 L60 98 M64 98 L68 98 M80 98 L88 98 M92 98 L100 98 ","strokeLinecap":"butt","stroke":"black","strokeWidth":4}
    </span>
  </span>
  <span>
    G: 
    {"x":38,"y":38}
    children: 
    <span>
      Defs: 
      {}
      children: 
      <span>
        ClipPath: 
        {"id":"clip-logo-background"}
        children: 
        <span>
          Rect: 
          {"width":24,"height":24,"rx":0,"ry":0}
        </span>
      </span>
      <span>
        ClipPath: 
        {"id":"clip-logo"}
        children: 
        <span>
          Rect: 
          {"width":20,"height":20,"rx":0,"ry":0}
        </span>
      </span>
    </span>
    <span>
      G: 
      {}
      children: 
      <span>
        Rect: 
        {"width":24,"height":24,"fill":"transparent","clipPath":"url(#clip-logo-background)"}
      </span>
    </span>
    <span>
      G: 
      {"x":2,"y":2}
      children: 
      <span>
        Image: 
        {"width":20,"height":20,"preserveAspectRatio":"xMidYMid slice","href":{"uri":"fakeUri"},"clipPath":"url(#clip-logo)"}
      </span>
    </span>
  </span>
</div>
`;

exports[`QRCode renders with segmented value 1`] = `
<div>
  <span>
    Defs: 
    {}
    children: 
    <span>
      LinearGradient: 
      {"id":"grad","x1":"0%","y1":"0%","x2":"100%","y2":"100%"}
      children: 
      <span>
        Stop: 
        {"offset":"0","stopColor":"rgb(255,0,0)","stopOpacity":"1"}
      </span>
      <span>
        Stop: 
        {"offset":"1","stopColor":"rgb(0,255,255)","stopOpacity":"1"}
      </span>
    </span>
  </span>
  <span>
    G: 
    {}
    children: 
    <span>
      Rect: 
      {"x":0,"y":0,"width":100,"height":100,"fill":"white"}
    </span>
  </span>
  <span>
    G: 
    {}
    children: 
    <span>
      Path: 
      {"d":"M0 2.380952380952381 L33.333333333333336 2.380952380952381 M38.095238095238095 2.380952380952381 L42.857142857142854 2.380952380952381 M47.61904761904762 2.380952380952381 L61.904761904761905 2.380952380952381 M66.66666666666667 2.380952380952381 L100 2.380952380952381 M0 7.142857142857142 L4.761904761904762 7.142857142857142 M28.57142857142857 7.142857142857142 L33.333333333333336 7.142857142857142 M38.095238095238095 7.142857142857142 L57.14285714285714 7.142857142857142 M66.66666666666667 7.142857142857142 L71.42857142857143 7.142857142857142 M95.23809523809524 7.142857142857142 L100 7.142857142857142 M0 11.904761904761905 L4.761904761904762 11.904761904761905 M9.523809523809524 11.904761904761905 L23.80952380952381 11.904761904761905 M28.57142857142857 11.904761904761905 L33.333333333333336 11.904761904761905 M38.095238095238095 11.904761904761905 L47.61904761904762 11.904761904761905 M52.38095238095238 11.904761904761905 L61.904761904761905 11.904761904761905 M66.66666666666667 11.904761904761905 L71.42857142857143 11.904761904761905 M76.19047619047619 11.904761904761905 L90.47619047619048 11.904761904761905 M95.23809523809524 11.904761904761905 L100 11.904761904761905 M0 16.666666666666664 L4.761904761904762 16.666666666666664 M9.523809523809524 16.666666666666664 L23.80952380952381 16.666666666666664 M28.57142857142857 16.666666666666664 L33.333333333333336 16.666666666666664 M47.61904761904762 16.666666666666664 L52.38095238095238 16.666666666666664 M57.14285714285714 16.666666666666664 L61.904761904761905 16.666666666666664 M66.66666666666667 16.666666666666664 L71.42857142857143 16.666666666666664 M76.19047619047619 16.666666666666664 L90.47619047619048 16.666666666666664 M95.23809523809524 16.666666666666664 L100 16.666666666666664 M0 21.428571428571427 L4.761904761904762 21.428571428571427 M9.523809523809524 21.428571428571427 L23.80952380952381 21.428571428571427 M28.57142857142857 21.428571428571427 L33.333333333333336 21.428571428571427 M38.095238095238095 21.428571428571427 L47.61904761904762 21.428571428571427 M57.14285714285714 21.428571428571427 L61.904761904761905 21.428571428571427 M66.66666666666667 21.428571428571427 L71.42857142857143 21.428571428571427 M76.19047619047619 21.428571428571427 L90.47619047619048 21.428571428571427 M95.23809523809524 21.428571428571427 L100 21.428571428571427 M0 26.19047619047619 L4.761904761904762 26.19047619047619 M28.57142857142857 26.19047619047619 L33.333333333333336 26.19047619047619 M52.38095238095238 26.19047619047619 L61.904761904761905 26.19047619047619 M66.66666666666667 26.19047619047619 L71.42857142857143 26.19047619047619 M95.23809523809524 26.19047619047619 L100 26.19047619047619 M0 30.95238095238095 L33.333333333333336 30.95238095238095 M38.095238095238095 30.95238095238095 L42.857142857142854 30.95238095238095 M47.61904761904762 30.95238095238095 L52.38095238095238 30.95238095238095 M57.14285714285714 30.95238095238095 L61.904761904761905 30.95238095238095 M66.66666666666667 30.95238095238095 L100 30.95238095238095 M47.61904761904762 35.714285714285715 L61.904761904761905 35.714285714285715 M0 40.476190476190474 L4.761904761904762 40.476190476190474 M14.285714285714285 40.476190476190474 L42.857142857142854 40.476190476190474 M57.14285714285714 40.476190476190474 L66.66666666666667 40.476190476190474 M76.19047619047619 40.476190476190474 L80.95238095238095 40.476190476190474 M85.71428571428571 40.476190476190474 L100 40.476190476190474 M0 45.238095238095234 L4.761904761904762 45.238095238095234 M33.333333333333336 45.238095238095234 L38.095238095238095 45.238095238095234 M47.61904761904762 45.238095238095234 L52.38095238095238 45.238095238095234 M57.14285714285714 45.238095238095234 L66.66666666666667 45.238095238095234 M76.19047619047619 45.238095238095234 L100 45.238095238095234 M4.761904761904762 50 L23.80952380952381 50 M28.57142857142857 50 L33.333333333333336 50 M42.857142857142854 50 L47.61904761904762 50 M66.66666666666667 50 L76.19047619047619 50 M80.95238095238095 50 L95.23809523809524 50 M4.761904761904762 54.76190476190476 L28.57142857142857 54.76190476190476 M33.333333333333336 54.76190476190476 L38.095238095238095 54.76190476190476 M42.857142857142854 54.76190476190476 L57.14285714285714 54.76190476190476 M61.904761904761905 54.76190476190476 L66.66666666666667 54.76190476190476 M76.19047619047619 54.76190476190476 L80.95238095238095 54.76190476190476 M85.71428571428571 54.76190476190476 L90.47619047619048 54.76190476190476 M0 59.52380952380952 L9.523809523809524 59.52380952380952 M14.285714285714285 59.52380952380952 L19.047619047619047 59.52380952380952 M28.57142857142857 59.52380952380952 L42.857142857142854 59.52380952380952 M47.61904761904762 59.52380952380952 L52.38095238095238 59.52380952380952 M66.66666666666667 59.52380952380952 L71.42857142857143 59.52380952380952 M85.71428571428571 59.52380952380952 L90.47619047619048 59.52380952380952 M38.095238095238095 64.28571428571429 L42.857142857142854 64.28571428571429 M52.38095238095238 64.28571428571429 L61.904761904761905 64.28571428571429 M66.66666666666667 64.28571428571429 L76.19047619047619 64.28571428571429 M85.71428571428571 64.28571428571429 L90.47619047619048 64.28571428571429 M95.23809523809524 64.28571428571429 L100 64.28571428571429 M0 69.04761904761905 L33.333333333333336 69.04761904761905 M38.095238095238095 69.04761904761905 L42.857142857142854 69.04761904761905 M57.14285714285714 69.04761904761905 L80.95238095238095 69.04761904761905 M90.47619047619048 69.04761904761905 L100 69.04761904761905 M0 73.80952380952381 L4.761904761904762 73.80952380952381 M28.57142857142857 73.80952380952381 L33.333333333333336 73.80952380952381 M38.095238095238095 73.80952380952381 L47.61904761904762 73.80952380952381 M52.38095238095238 73.80952380952381 L71.42857142857143 73.80952380952381 M76.19047619047619 73.80952380952381 L80.95238095238095 73.80952380952381 M90.47619047619048 73.80952380952381 L95.23809523809524 73.80952380952381 M0 78.57142857142857 L4.761904761904762 78.57142857142857 M9.523809523809524 78.57142857142857 L23.80952380952381 78.57142857142857 M28.57142857142857 78.57142857142857 L33.333333333333336 78.57142857142857 M38.095238095238095 78.57142857142857 L76.19047619047619 78.57142857142857 M80.95238095238095 78.57142857142857 L85.71428571428571 78.57142857142857 M95.23809523809524 78.57142857142857 L100 78.57142857142857 M0 83.33333333333333 L4.761904761904762 83.33333333333333 M9.523809523809524 83.33333333333333 L23.80952380952381 83.33333333333333 M28.57142857142857 83.33333333333333 L33.333333333333336 83.33333333333333 M38.095238095238095 83.33333333333333 L42.857142857142854 83.33333333333333 M47.61904761904762 83.33333333333333 L61.904761904761905 83.33333333333333 M66.66666666666667 83.33333333333333 L90.47619047619048 83.33333333333333 M0 88.09523809523809 L4.761904761904762 88.09523809523809 M9.523809523809524 88.09523809523809 L23.80952380952381 88.09523809523809 M28.57142857142857 88.09523809523809 L33.333333333333336 88.09523809523809 M47.61904761904762 88.09523809523809 L52.38095238095238 88.09523809523809 M61.904761904761905 88.09523809523809 L80.95238095238095 88.09523809523809 M85.71428571428571 88.09523809523809 L100 88.09523809523809 M0 92.85714285714286 L4.761904761904762 92.85714285714286 M28.57142857142857 92.85714285714286 L33.333333333333336 92.85714285714286 M42.857142857142854 92.85714285714286 L52.38095238095238 92.85714285714286 M61.904761904761905 92.85714285714286 L71.42857142857143 92.85714285714286 M80.95238095238095 92.85714285714286 L95.23809523809524 92.85714285714286 M0 97.61904761904762 L33.333333333333336 97.61904761904762 M38.095238095238095 97.61904761904762 L42.857142857142854 97.61904761904762 M52.38095238095238 97.61904761904762 L57.14285714285714 97.61904761904762 M61.904761904761905 97.61904761904762 L66.66666666666667 97.61904761904762 M71.42857142857143 97.61904761904762 L95.23809523809524 97.61904761904762 ","strokeLinecap":"butt","stroke":"black","strokeWidth":4.761904761904762}
    </span>
  </span>
</div>
`;
